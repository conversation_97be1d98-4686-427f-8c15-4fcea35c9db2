import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class ReportCard extends StatelessWidget {
  const ReportCard({
    super.key,
    required this.title,
    required this.path,
  });
  final String title;
  // final IconData icon;
  final String path;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 3,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          const SizedBox(
            height: 20,
          ),
          Container(
            height: 70,
            // child: Icon(icon, size: 35, color: Colors.redAccent),
            child: SvgPicture.asset(path),
            padding: const EdgeInsets.all(12),
          ),
          const Spacer(),
          Container(
            width: double.maxFinite,
            decoration: const BoxDecoration(
                color: Colors.redAccent,
                borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12))),
            child: Center(
                child: Text(
              overflow: TextOverflow.ellipsis,
              title,
              style: const TextStyle(color: Colors.white),
            )),
            padding: const EdgeInsets.all(12),
          )
        ],
      ),
    );
  }
}
