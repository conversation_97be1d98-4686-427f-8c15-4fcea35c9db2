import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class SalesReport extends StatefulWidget {
  const SalesReport({super.key});

  @override
  State<SalesReport> createState() => _SalesReportState();
}

class _SalesReportState extends State<SalesReport> {
  String? selectedworkshop;
  List<CustomersModel> cusfilteredList3 = [];

  // Sales Report Data
  bool isLoading = false;
  List<RepairOrderModel> repairOrdersData = [];
  List<RepairOrderModel> searchedRepairOrdersData = [];
  DateTimeRange selectedDateRange = DateTimeRange(
      start: DateTime.utc(
              DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .subtract(const Duration(days: 30)),
      end: DateTime.utc(
          DateTime.now().year, DateTime.now().month, DateTime.now().day));

  // PlutoGrid variables
  late PlutoGridStateManager stateManager;
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];

  @override
  void initState() {
    super.initState();
    _initializeColumns();
  }

  void _initializeColumns() {
    columns = [
      PlutoColumn(
        title: 'Sr No',
        field: 'srNo',
        type: PlutoColumnType.number(),
        width: 80,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Date',
        field: 'date',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Invoice No',
        field: 'invoiceNo',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Job-card Number',
        field: 'jobCardNumber',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Customer Name',
        field: 'customerName',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Vehicle Number',
        field: 'vehicleNumber',
        type: PlutoColumnType.text(),
        width: 130,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Make',
        field: 'make',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Model',
        field: 'model',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Service / Part Name',
        field: 'servicePartName',
        type: PlutoColumnType.text(),
        width: 180,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Service / Part Category',
        field: 'servicePartCategory',
        type: PlutoColumnType.text(),
        width: 180,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Qty',
        field: 'qty',
        type: PlutoColumnType.text(),
        width: 80,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'GST Number',
        field: 'gstNumber',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Gross Value',
        field: 'grossValue',
        type: PlutoColumnType.currency(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Discount',
        field: 'discount',
        type: PlutoColumnType.currency(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Taxable Amount',
        field: 'taxableAmount',
        type: PlutoColumnType.currency(),
        width: 130,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Tax Rate',
        field: 'taxRate',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'CGST',
        field: 'cgst',
        type: PlutoColumnType.currency(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'SGST',
        field: 'sgst',
        type: PlutoColumnType.currency(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'IGST',
        field: 'igst',
        type: PlutoColumnType.currency(),
        width: 100,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Total',
        field: 'total',
        type: PlutoColumnType.currency(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Received Amount',
        field: 'receivedAmount',
        type: PlutoColumnType.currency(),
        width: 140,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Payment Details',
        field: 'paymentDetails',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
    ];
  }

  getdplist(String value) async {
    try {
      cusfilteredList3 = (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();

      setState(() {});
      print('>>>>>>> ${cusfilteredList3.length}');
    } catch (e) {
      debugPrint('Error in getdplist: $e');
    }
  }

  Future<void> fetchSalesReport() async {
    try {
      print('Starting fetchSalesReport...');

      setState(() {
        isLoading = true;
      });

      rows.clear();
      repairOrdersData.clear();

      QuerySnapshot<Map<String, dynamic>> ordersQuerySnapshot =
          await FirebaseFirestore.instance
              .collection('Orders')
              .where("franchiseId", isEqualTo: selectedworkshop)
              .where('completionDate',
                  isGreaterThanOrEqualTo: selectedDateRange.start)
              .where('completionDate',
                  isLessThanOrEqualTo:
                      selectedDateRange.end.add(const Duration(days: 1)))
              .orderBy('completionDate', descending: true)
              .get();

      print('Found ${ordersQuerySnapshot.docs.length} orders');

      for (QueryDocumentSnapshot<Map<String, dynamic>> orderDoc
          in ordersQuerySnapshot.docs) {
        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromMap(orderDoc.id, orderDoc.data());

        repairOrdersData.add(repairOrderModel);
      }

      print('Added ${repairOrdersData.length} repair orders to list');

      if (repairOrdersData.isNotEmpty) {
        generateDataForTable();
        print('Generated ${rows.length} table rows');
      } else {
        print('No repair orders found');
      }

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching sales report: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void generateDataForTable() {
    rows.clear();
    int srNo = 1;

    for (RepairOrderModel repairOrder in repairOrdersData) {
      // Get all parts and services for this repair order
      List<dynamic> allItems = [];
      allItems.addAll(repairOrder.repairDetailsModel?.parts ?? []);
      allItems.addAll(repairOrder.repairDetailsModel?.services ?? []);

      if (allItems.isEmpty) {
        // If no parts or services, create one row with basic info
        PlutoRow row = PlutoRow(
          cells: {
            'srNo': PlutoCell(value: srNo),
            'date': PlutoCell(
                value: repairOrder.completionDate
                        ?.toDate()
                        .toString()
                        .split(' ')[0] ??
                    "NA"),
            'invoiceNo':
                PlutoCell(value: repairOrder.invoiceId?.toString() ?? "NA"),
            'jobCardNumber':
                PlutoCell(value: repairOrder.jobCardId?.toString() ?? "NA"),
            'customerName': PlutoCell(
                value: repairOrder.customerDetailsModel?.username ?? "NA"),
            'vehicleNumber': PlutoCell(
                value: repairOrder.vehicleDetailsModel?.registrationNumber ??
                    "NA"),
            'make':
                PlutoCell(value: repairOrder.vehicleDetailsModel?.make ?? "NA"),
            'model': PlutoCell(
                value: repairOrder.vehicleDetailsModel?.model ?? "NA"),
            'servicePartName': PlutoCell(value: "No items"),
            'servicePartCategory': PlutoCell(value: "NA"),
            'qty': PlutoCell(value: "1"),
            'gstNumber': PlutoCell(
                value: repairOrder.customerDetailsModel?.gstin ?? "NA"),
            'grossValue': PlutoCell(value: 0.00),
            'discount': PlutoCell(
                value: repairOrder.repairDetailsModel?.discount ?? 0.00),
            'taxableAmount': PlutoCell(value: 0.00),
            'taxRate': PlutoCell(value: "0%"),
            'cgst': PlutoCell(value: 0.00),
            'sgst': PlutoCell(value: 0.00),
            'igst': PlutoCell(value: 0.00),
            'total':
                PlutoCell(value: repairOrder.repairDetailsModel?.total ?? 0.00),
            'receivedAmount': PlutoCell(
                value: repairOrder.repairDetailsModel?.paymentReceived ?? 0.00),
            'paymentDetails': PlutoCell(
                value: repairOrder.paymentMode?.name.toString() ?? "NA"),
          },
        );
        rows.add(row);
        srNo++;
      } else {
        // Create a row for each part/service
        for (var item in allItems) {
          double itemPrice = 0.0;
          double itemTax = 0.0;
          double taxRate = 0.0;
          String itemName = "";
          String category = "";

          if (item is ServicePartModel) {
            // Check if it's a part or service based on available fields
            if (item.purchasePrice != null && item.purchasePrice! > 0) {
              // It's a part
              itemPrice = item.purchasePrice ?? 0.0;
              itemTax = item.partsGst ?? 0.0;
              itemName = item.title ?? "Unknown Part";
              category = "Parts";
              taxRate = (item.gstRate ?? 0).toDouble();
            } else if (item.rate != null && item.rate! > 0) {
              // It's a service
              itemPrice = item.rate ?? 0.0;
              itemTax = item.servicesGst ?? 0.0;
              itemName = item.title ?? "Unknown Service";
              category = "Service";
              taxRate = (item.gstRate ?? 0).toDouble();
            } else {
              // Fallback - check which GST field has value
              if (item.partsGst != null && item.partsGst! > 0) {
                itemPrice = item.amount ?? 0.0;
                itemTax = item.partsGst ?? 0.0;
                itemName = item.title ?? "Unknown Part";
                category = "Parts";
                taxRate = (item.gstRate ?? 0).toDouble();
              } else {
                itemPrice = item.amount ?? 0.0;
                itemTax = item.servicesGst ?? 0.0;
                itemName = item.title ?? "Unknown Service";
                category = "Service";
                taxRate = (item.gstRate ?? 0).toDouble();
              }
            }
          }

          double taxableAmount = itemPrice;
          double cgst = ((repairOrder.gstIncluded ?? false) &&
                  !(repairOrder.isigst ?? false))
              ? (itemTax / 2)
              : 0.00;
          double sgst = ((repairOrder.gstIncluded ?? false) &&
                  !(repairOrder.isigst ?? false))
              ? (itemTax / 2)
              : 0.00;
          double igst = ((repairOrder.gstIncluded ?? false) &&
                  (repairOrder.isigst ?? false))
              ? itemTax
              : 0.00;

          PlutoRow row = PlutoRow(
            cells: {
              'srNo': PlutoCell(value: srNo),
              'date': PlutoCell(
                  value: repairOrder.completionDate
                          ?.toDate()
                          .toString()
                          .split(' ')[0] ??
                      "NA"),
              'invoiceNo':
                  PlutoCell(value: repairOrder.invoiceId?.toString() ?? "NA"),
              'jobCardNumber':
                  PlutoCell(value: repairOrder.jobCardId?.toString() ?? "NA"),
              'customerName': PlutoCell(
                  value: repairOrder.customerDetailsModel?.username ?? "NA"),
              'vehicleNumber': PlutoCell(
                  value: repairOrder.vehicleDetailsModel?.registrationNumber ??
                      "NA"),
              'make': PlutoCell(
                  value: repairOrder.vehicleDetailsModel?.make ?? "NA"),
              'model': PlutoCell(
                  value: repairOrder.vehicleDetailsModel?.model ?? "NA"),
              'servicePartName': PlutoCell(value: itemName),
              'servicePartCategory': PlutoCell(value: category),
              'qty': PlutoCell(value: item.quantity?.toString() ?? "1"),
              'gstNumber': PlutoCell(
                  value: repairOrder.customerDetailsModel?.gstin ?? "NA"),
              'grossValue': PlutoCell(value: itemPrice),
              'discount': PlutoCell(
                  value: repairOrder.repairDetailsModel?.discount ?? 0.00),
              'taxableAmount': PlutoCell(value: taxableAmount),
              'taxRate': PlutoCell(value: "${taxRate.toStringAsFixed(1)}%"),
              'cgst': PlutoCell(value: cgst),
              'sgst': PlutoCell(value: sgst),
              'igst': PlutoCell(value: igst),
              'total': PlutoCell(value: itemPrice + itemTax),
              'receivedAmount': PlutoCell(
                  value:
                      repairOrder.repairDetailsModel?.paymentReceived ?? 0.00),
              'paymentDetails': PlutoCell(
                  value: repairOrder.paymentMode?.name.toString() ?? "NA"),
            },
          );

          rows.add(row);
          srNo++;
        }
      }
    }
  }

  void _showDateRangePopup() {
    DateTime tempStartDate = selectedDateRange.start;
    DateTime tempEndDate = selectedDateRange.end;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              backgroundColor: Colors.white,
              title: const Text(
                'Select Date Range',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SizedBox(
                width: 400,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Start Date
                    const Text(
                      'Start Date',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () async {
                        DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: tempStartDate,
                          firstDate: DateTime(2020),
                          lastDate: tempEndDate,
                        );
                        if (picked != null) {
                          setDialogState(() {
                            tempStartDate = picked;
                          });
                        }
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 20),
                            const SizedBox(width: 12),
                            Text(
                              tempStartDate.toString().split(' ')[0],
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // End Date
                    const Text(
                      'End Date',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () async {
                        DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: tempEndDate,
                          firstDate: tempStartDate,
                          lastDate: DateTime.now(),
                        );
                        if (picked != null) {
                          setDialogState(() {
                            tempEndDate = picked;
                          });
                        }
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 20),
                            const SizedBox(width: 12),
                            Text(
                              tempEndDate.toString().split(' ')[0],
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 228, 60, 60),
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    setState(() {
                      selectedDateRange = DateTimeRange(
                        start: tempStartDate,
                        end: tempEndDate,
                      );
                    });
                    Navigator.of(context).pop();
                    if (selectedworkshop != null) {
                      fetchSalesReport();
                    }
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void searchFromTable({required String searchTerm}) {
    if (searchTerm.isEmpty) {
      // Reset to show all data
      generateDataForTable();
      setState(() {});
    } else {
      // Filter data based on search term
      List<PlutoRow> filteredRows = rows.where((row) {
        String customerName =
            row.cells['customerName']?.value?.toString().toLowerCase() ?? '';
        String jobCardNumber =
            row.cells['jobCardNumber']?.value?.toString().toLowerCase() ?? '';
        String vehicleNumber =
            row.cells['vehicleNumber']?.value?.toString().toLowerCase() ?? '';
        String make = row.cells['make']?.value?.toString().toLowerCase() ?? '';
        String model =
            row.cells['model']?.value?.toString().toLowerCase() ?? '';
        String servicePartName =
            row.cells['servicePartName']?.value?.toString().toLowerCase() ?? '';

        String searchLower = searchTerm.toLowerCase();

        return customerName.contains(searchLower) ||
            jobCardNumber.contains(searchLower) ||
            vehicleNumber.contains(searchLower) ||
            make.contains(searchLower) ||
            model.contains(searchLower) ||
            servicePartName.contains(searchLower);
      }).toList();

      rows = filteredRows;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      ctrl.franchises.sort((a, b) => (a.garageName?.toLowerCase().trim() ?? '')
          .compareTo(b.garageName?.toLowerCase().trim() ?? ''));
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              DropdownButtonHideUnderline(
                  child: DropdownButtonFormField(
                isExpanded: true,
                focusColor: Colors.transparent,
                dropdownColor: Colors.white,
                decoration: InputDecoration(
                    hintText: "Select Workshop",
                    hintStyle: const TextStyle(fontSize: 30),
                    constraints:
                        const BoxConstraints(maxWidth: 300, maxHeight: 45),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5))),
                value: selectedworkshop,
                items: [
                  ...List.generate(
                    ctrl.franchises.length,
                    (index) {
                      return DropdownMenuItem(
                          value: ctrl.franchises[index].docID,
                          child: Text(ctrl.franchises[index].garageName ?? ""));
                    },
                  ),
                ],
                onChanged: (value) async {
                  if (value == null) {
                    return;
                  }
                  selectedworkshop = value;
                  fetchSalesReport();
                  // getdplist(value);
                },
              )),
              const SizedBox(
                height: 25,
              ),
              if (selectedworkshop != null) ...[
                Row(
                  children: [
                    SizedBox(
                      width: 600,
                      child: TextField(
                        decoration: const InputDecoration(
                          hintText:
                              'Search by customer name, invoice, vehicle...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          searchFromTable(searchTerm: value);
                        },
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton(
                      style: ButtonStyle(
                          backgroundColor: const WidgetStatePropertyAll(
                              Color.fromARGB(255, 228, 60, 60)),
                          padding: const WidgetStatePropertyAll(
                              EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 20)),
                          side: const WidgetStatePropertyAll(
                              BorderSide(color: Colors.transparent)),
                          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ))),
                      onPressed: () {
                        _showDateRangePopup();
                      },
                      child: Row(
                        children: [
                          Text(
                            'Date',
                            style: const TextStyle(color: Colors.white),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Icon(
                            Icons.edit,
                            color: Colors.white,
                          ),
                        ],
                      ),
                      // child: Text(
                      //   '${selectedDateRange.start.toString().split(' ')[0]} - ${selectedDateRange.end.toString().split(' ')[0]}',
                      //   style: const TextStyle(color: Colors.white),
                      // ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 25,
                ),
                // PlutoGrid Table
                Container(
                  // height: 600, // Fixed height for the grid
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: isLoading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : rows.isEmpty
                          ? const Center(
                              child: Padding(
                                padding: EdgeInsets.all(20.0),
                                child: Text(
                                  'No sales data found for the selected date range',
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.grey),
                                ),
                              ),
                            )
                          : PlutoGrid(
                              columns: columns,
                              rows: rows,
                              onLoaded: (PlutoGridOnLoadedEvent event) {
                                stateManager = event.stateManager;
                              },
                              configuration: PlutoGridConfiguration(
                                style: PlutoGridStyleConfig(
                                  gridBorderColor: Colors.grey.shade300,
                                  activatedBorderColor: Colors.blue,
                                  rowColor: Colors.white,
                                  evenRowColor: Colors.grey.shade50,
                                  columnTextStyle: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  cellTextStyle: const TextStyle(
                                    fontSize: 11,
                                  ),
                                ),
                                columnSize: const PlutoGridColumnSizeConfig(
                                  autoSizeMode: PlutoAutoSizeMode.none,
                                  resizeMode: PlutoResizeMode.normal,
                                ),
                                scrollbar: const PlutoGridScrollbarConfig(
                                  isAlwaysShown: true,
                                  scrollbarThickness: 8,
                                ),
                              ),
                            ),
                ),
              ]
            ],
          ),
        ),
      );
    });
  }
}
