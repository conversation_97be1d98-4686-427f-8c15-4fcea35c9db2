import 'package:flutter/material.dart';
import 'package:speed_force_admin/models/part_model.dart';

class PartsTile extends StatefulWidget {
  const PartsTile({
    super.key,
    required this.parts,
    required this.onEdit,
    required this.onDelete,
  });
  
  final List<PartModel> parts;
  final Function(PartModel) onEdit;
  final Function(PartModel) onDelete;

  @override
  State<PartsTile> createState() => _PartsTileState();
}

class _PartsTileState extends State<PartsTile> {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.parts.length,
      itemBuilder: (context, index) {
        final part = widget.parts[index];
        
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: index % 2 != 0
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[200])
              : null,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                  width: 70,
                  child: Text(
                      (index + 1).toString(),
                      textAlign: TextAlign.center)),
              const SizedBox(width: 20),
              Expanded(
                child: Text(
                    overflow: TextOverflow.ellipsis,
                    part.name ?? ""),
              ),
              const SizedBox(width: 60),
              Expanded(
                child: Text(
                    overflow: TextOverflow.ellipsis,
                    part.category ?? ""),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                    overflow: TextOverflow.ellipsis,
                    part.manufacturer ?? ""),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  part.mrp != null ? '₹${part.mrp!.toStringAsFixed(2)}' : '-',
                  maxLines: 1,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  part.purchasePrice != null ? '₹${part.purchasePrice!.toStringAsFixed(2)}' : '-',
                  maxLines: 1,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  part.gstRate != null ? '${part.gstRate!.toStringAsFixed(1)}%' : '-',
                  maxLines: 1,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => widget.onEdit(part),
                      tooltip: 'Edit Part',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => widget.onDelete(part),
                      tooltip: 'Delete Part',
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
