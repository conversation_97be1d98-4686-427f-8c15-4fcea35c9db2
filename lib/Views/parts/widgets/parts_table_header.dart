import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';

class PartsTableHeader extends StatelessWidget {
  const PartsTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 228, 228, 228),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
              width: 70,
              child: Text('Sr No',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontWeight: FontWeight.w600))),
          SizedBox(width: 20),
          Expanded(child: TableHeaderText(headerName: 'Part Name')),
          SizedBox(width: 60),
          Expanded(child: TableHeaderText(headerName: 'Category')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'Manufacturer')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'MRP (₹)')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'Purchase Price (₹)')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'GST Rate (%)')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: '')),
        ],
      ),
    );
  }
}
