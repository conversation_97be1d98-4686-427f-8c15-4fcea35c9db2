import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/services/widgets/services_table_header.dart';
import 'package:speed_force_admin/Views/services/widgets/services_tile.dart';
import 'package:speed_force_admin/models/service_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class Services extends StatefulWidget {
  const Services({super.key});

  @override
  State<Services> createState() => _ServicesState();
}

class _ServicesState extends State<Services> {
  // Controllers
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _serviceNameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _gstRateController = TextEditingController();

  // State variables
  List<ServiceModel> allServices = [];
  List<ServiceModel> filteredServices = [];
  bool isLoading = false;
  bool isAddingService = false;
  String? serviceNameError;
  String? priceError;
  String? gstRateError;
  double? selectedGstRate;
  Timer? _debounceTimer;

  // Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _fetchServices();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _serviceNameController.dispose();
    _priceController.dispose();
    _gstRateController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _filterServices(_searchController.text);
    });
  }

  void _filterServices(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredServices = List.from(allServices);
      } else {
        filteredServices = allServices.where((service) {
          return service.name!.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _fetchServices() async {
    if (!mounted) return;
    setState(() {
      isLoading = true;
    });

    try {
      final QuerySnapshot snapshot = await FBFireStore.services
          .where('franchiseId', isEqualTo: null)
          .orderBy('name')
          .get();

      if (mounted) {
        setState(() {
          // Filter to show only admin services (franchiseId is null)
          allServices = snapshot.docs
              .map((doc) =>
                  ServiceModel.fromMap(doc.data() as Map<String, dynamic>))
              .where((service) => service.franchiseId == null)
              .toList();
          filteredServices = List.from(allServices);
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Error fetching services: $e');
      }
    }
  }

  Future<bool> _checkServiceNameExists(String serviceName) async {
    try {
      // Query for exact match with admin services only (franchiseId is null)
      final QuerySnapshot snapshot = await FBFireStore.services
          .where('name', isEqualTo: serviceName.toUpperCase())
          // .where('franchiseId', isNull: true)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  void _validateServiceName(String value) {
    setState(() {
      if (value.isEmpty) {
        serviceNameError = 'Service name is required';
      } else if (value.length < 2) {
        serviceNameError = 'Service name must be at least 2 characters';
      } else {
        serviceNameError = null;
      }
    });
  }

  void _validatePrice(String value) {
    setState(() {
      if (value.isEmpty) {
        priceError = 'Price is required';
      } else {
        final double? price = double.tryParse(value);
        if (price == null || price <= 0) {
          priceError = 'Please enter a valid price';
        } else {
          priceError = null;
        }
      }
    });
  }

  Future<void> _addService() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final serviceName = _serviceNameController.text.trim();
    final price = double.tryParse(_priceController.text.trim());

    if (serviceName.isEmpty || price == null || selectedGstRate == null) {
      _showErrorSnackBar('Please fill all required fields correctly');
      return;
    }

    setState(() {
      isAddingService = true;
    });

    try {
      // Check if service name already exists (case-insensitive)
      final bool exists = await _checkServiceNameExists(serviceName);
      if (exists) {
        setState(() {
          serviceNameError = 'Service with this name already exists';
          isAddingService = false;
        });
        return;
      }

      final String serviceId = FBFireStore.services.doc().id;
      final ServiceModel newService = ServiceModel(
        category: 'labour',
        serviceId: serviceId,
        name: serviceName.toUpperCase(), // Store in uppercase
        price: price,
        gstRate: selectedGstRate,
      );

      await FBFireStore.services.doc(serviceId).set(newService.toMap());

      if (mounted) {
        setState(() {
          allServices.add(newService);
          _filterServices(_searchController.text);
          isAddingService = false;
        });

        _clearForm();
        context.pop();
        _showSuccessSnackBar('Service added successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isAddingService = false;
        });
        _showErrorSnackBar('Error adding service: $e');
      }
    }
  }

  void _clearForm() {
    _serviceNameController.clear();
    _priceController.clear();
    _gstRateController.clear();
    setState(() {
      serviceNameError = null;
      priceError = null;
      gstRateError = null;
      selectedGstRate = null;
    });
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                PageHeaderWithButton(
                  title:
                      "Services (${allServices.isEmpty ? '' : allServices.length})",
                  onPressed: () {},
                  button: false,
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddServiceDialog(),
                  icon: const Icon(Icons.add, color: Colors.white),
                  label: const Text(
                    'Add Service',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 222, 61, 61),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 450),
              child: SizedBox(
                height: 45,
                child: TextFormField(
                  controller: _searchController,
                  cursorHeight: 20,
                  onChanged: (value) {
                    _onSearchChanged();
                  },
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(),
                    fillColor: Colors.grey.shade100,
                    filled: true,
                    prefixIcon: const Icon(
                      Icons.search,
                      size: 22,
                      color: Colors.blue,
                    ),
                    border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.circular(7)),
                    hintText: 'Search services by name...',
                    hintStyle: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ),
            // SearchField(
            //   searchController: _searchController,
            //   onChanged: (value) {
            //     _onSearchChanged();
            //   },
            // ),
            // Search Bar
            // Container(
            //   width: 600,
            //   padding: const EdgeInsets.symmetric(horizontal: 16),
            //   decoration: BoxDecoration(
            //     color: Colors.white,
            //     borderRadius: BorderRadius.circular(8),
            //     border: Border.all(color: Colors.grey[300]!),
            //   ),
            //   child: TextField(
            //     controller: _searchController,
            //     decoration: const InputDecoration(
            //       hintText: 'Search services by name...',
            //       border: InputBorder.none,
            //       icon: Icon(Icons.search, color: Colors.grey),
            //     ),
            //   ),
            // ),
            const SizedBox(height: 20),

            // Services List
            Expanded(
              child: isLoading
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : filteredServices.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                allServices.isEmpty
                                    ? 'No services found. Add your first service!'
                                    : 'No services match your search.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Column(
                            children: [
                              const SizedBox(height: 25),
                              const ServicesTableHeader(),
                              Expanded(
                                child: ServicesTile(
                                  services: filteredServices,
                                  onEdit: _editService,
                                  onDelete: _deleteService,
                                ),
                              ),
                            ],
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddServiceDialog() {
    _clearForm();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              backgroundColor: Colors.white,
              title: const Text(
                'Add New Service',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SizedBox(
                width: 400,
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service Name Field
                      const Text(
                        'Service Name *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _serviceNameController,
                        decoration: InputDecoration(
                          hintText: 'Enter service name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: serviceNameError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validateServiceName(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Service name is required';
                          }
                          if (value.trim().length < 2) {
                            return 'Service name must be at least 2 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Price Field
                      const Text(
                        'Price (MRP) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _priceController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: 'Enter price',
                          prefixText: '₹ ',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: priceError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validatePrice(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Price is required';
                          }
                          final double? price = double.tryParse(value.trim());
                          if (price == null || price <= 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // GST Rate Field
                      const Text(
                        'GST Rate (%) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<double>(
                        value: selectedGstRate,
                        decoration: InputDecoration(
                          hintText: 'Select GST rate',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(value: 0, child: Text('0%')),
                          DropdownMenuItem(value: 5, child: Text('5%')),
                          DropdownMenuItem(value: 12, child: Text('12%')),
                          DropdownMenuItem(value: 18, child: Text('18%')),
                          DropdownMenuItem(value: 28, child: Text('28%')),
                        ],
                        onChanged: (value) {
                          setDialogState(() {
                            selectedGstRate = value;
                            gstRateError = null;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a GST rate';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _clearForm();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAddingService
                      ? null
                      : () async {
                          await _addService();
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  child: isAddingService
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Add Service',
                          style: TextStyle(color: Colors.white),
                        ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _editService(ServiceModel service) {
    _serviceNameController.text = service.name ?? '';
    _priceController.text = service.price?.toString() ?? '';
    selectedGstRate = service.gstRate?.toDouble();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              backgroundColor: Colors.white,
              title: const Text(
                'Edit Service',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SizedBox(
                width: 400,
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service Name Field
                      const Text(
                        'Service Name *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _serviceNameController,
                        decoration: InputDecoration(
                          hintText: 'Enter service name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: serviceNameError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validateServiceName(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Service name is required';
                          }
                          if (value.trim().length < 2) {
                            return 'Service name must be at least 2 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Price Field
                      const Text(
                        'Price (MRP) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _priceController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: 'Enter price',
                          prefixText: '₹ ',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: priceError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validatePrice(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Price is required';
                          }
                          final double? price = double.tryParse(value.trim());
                          if (price == null || price <= 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // GST Rate Field
                      const Text(
                        'GST Rate (%) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<double>(
                        value: selectedGstRate,
                        decoration: InputDecoration(
                          hintText: 'Select GST rate',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(value: 0, child: Text('0%')),
                          DropdownMenuItem(value: 5, child: Text('5%')),
                          DropdownMenuItem(value: 12, child: Text('12%')),
                          DropdownMenuItem(value: 18, child: Text('18%')),
                          DropdownMenuItem(value: 28, child: Text('28%')),
                        ],
                        onChanged: (value) {
                          setDialogState(() {
                            selectedGstRate = value;
                            gstRateError = null;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a GST rate';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _clearForm();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAddingService
                      ? null
                      : () async {
                          await _updateService(service);
                          if (mounted && serviceNameError == null) {
                            Navigator.of(context).pop();
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: isAddingService
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Update Service',
                          style: TextStyle(color: Colors.white),
                        ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updateService(ServiceModel service) async {
    if (!_formKey.currentState!.validate()) return;

    final serviceName = _serviceNameController.text.trim();
    final price = double.tryParse(_priceController.text.trim());

    if (serviceName.isEmpty || price == null || selectedGstRate == null) {
      _showErrorSnackBar('Please fill all required fields correctly');
      return;
    }

    setState(() {
      isAddingService = true;
    });

    try {
      // Check if service name already exists (case-insensitive) for other services
      if (serviceName.toLowerCase() != service.name?.toLowerCase()) {
        final bool exists = await _checkServiceNameExists(serviceName);
        if (exists) {
          setState(() {
            serviceNameError = 'Service with this name already exists';
            isAddingService = false;
          });
          return;
        }
      }

      final ServiceModel updatedService = ServiceModel(
        serviceId: service.serviceId,
        name: serviceName.toUpperCase(), // Store in uppercase
        price: price,
        gstRate: selectedGstRate,
      );

      await FBFireStore.services
          .doc(service.serviceId)
          .update(updatedService.toMap());

      if (mounted) {
        setState(() {
          final index =
              allServices.indexWhere((s) => s.serviceId == service.serviceId);
          if (index != -1) {
            allServices[index] = updatedService;
          }
          _filterServices(_searchController.text);
          isAddingService = false;
        });

        _clearForm();
        _showSuccessSnackBar('Service updated successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isAddingService = false;
        });
        _showErrorSnackBar('Error updating service: $e');
      }
    }
  }

  void _deleteService(ServiceModel service) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'Delete Service',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Are you sure you want to delete this ${service.name}?'),
              const SizedBox(height: 16),
              // Container(
              //   padding: const EdgeInsets.all(12),
              //   decoration: BoxDecoration(
              //     color: Colors.grey[100],
              //     borderRadius: BorderRadius.circular(8),
              //   ),
              //   child: Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       Text(
              //         'Service: ${service.name}',
              //         style: const TextStyle(fontWeight: FontWeight.bold),
              //       ),
              //       Text('Price: ₹${service.price?.toStringAsFixed(2)}'),
              //       Text('GST Rate: ${service.gstRate?.toStringAsFixed(1)}%'),
              //     ],
              //   ),
              // ),
              // const SizedBox(height: 16),
              // const Text(
              //   'This action cannot be undone.',
              //   style: TextStyle(
              //     color: Colors.red,
              //     fontWeight: FontWeight.w500,
              // ),
              // ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await FBFireStore.services.doc(service.serviceId).delete();

                  if (mounted) {
                    setState(() {
                      allServices
                          .removeWhere((s) => s.serviceId == service.serviceId);
                      _filterServices(_searchController.text);
                    });

                    Navigator.of(context).pop();
                    _showSuccessSnackBar('Service deleted successfully');
                  }
                } catch (e) {
                  if (mounted) {
                    Navigator.of(context).pop();
                    _showErrorSnackBar('Error deleting service: $e');
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text(
                'Delete',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
