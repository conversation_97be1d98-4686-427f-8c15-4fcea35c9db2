import 'package:flutter/material.dart';
import 'package:speed_force_admin/models/service_model.dart';

class ServicesTile extends StatefulWidget {
  const ServicesTile({
    super.key,
    required this.services,
    required this.onEdit,
    required this.onDelete,
  });
  
  final List<ServiceModel> services;
  final Function(ServiceModel) onEdit;
  final Function(ServiceModel) onDelete;

  @override
  State<ServicesTile> createState() => _ServicesTileState();
}

class _ServicesTileState extends State<ServicesTile> {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.services.length,
      itemBuilder: (context, index) {
        final service = widget.services[index];
        
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: index % 2 != 0
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[200])
              : null,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                  width: 70,
                  child: Text(
                      (index + 1).toString(),
                      textAlign: TextAlign.center)),
              const SizedBox(width: 20),
              Expanded(
                child: Text(
                    overflow: TextOverflow.ellipsis,
                    service.name ?? ""),
              ),
              const SizedBox(width: 60),
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  service.price != null ? '₹${service.price!.toStringAsFixed(2)}' : '-',
                  maxLines: 1,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  service.gstRate != null ? '${service.gstRate!.toStringAsFixed(1)}%' : '-',
                  maxLines: 1,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => widget.onEdit(service),
                      tooltip: 'Edit Service',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => widget.onDelete(service),
                      tooltip: 'Delete Service',
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
